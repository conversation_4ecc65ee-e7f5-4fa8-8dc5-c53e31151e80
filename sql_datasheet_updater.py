#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SQL Datasheet URL Updater
用于读取SQL文件并生成UPDATE语句来更新datasheet_url字段
适用于Cloudflare D1数据库
"""

import os
import re
import glob
from datetime import datetime
from typing import List, Tuple, Dict


def extract_insert_statements(sql_content: str) -> List[str]:
    """
    从SQL内容中提取所有INSERT OR REPLACE语句
    """
    # 匹配INSERT OR REPLACE语句的正则表达式
    pattern = r"INSERT OR REPLACE INTO products202503.*?VALUES\s*\([^;]+\);"
    matches = re.findall(pattern, sql_content, re.DOTALL | re.IGNORECASE)
    return matches


def parse_insert_statement(insert_stmt: str) -> Tuple[str, str]:
    """
    解析INSERT语句，提取product_id和datasheet_url
    返回: (product_id, datasheet_url)
    """
    # 提取VALUES部分的内容
    values_match = re.search(r"VALUES\s*\(([^)]+)\)", insert_stmt, re.DOTALL)
    if not values_match:
        return None, None
    
    values_content = values_match.group(1)
    
    # 分割字段值，考虑到字符串中可能包含逗号
    values = []
    current_value = ""
    in_quotes = False
    quote_char = None
    bracket_count = 0
    
    i = 0
    while i < len(values_content):
        char = values_content[i]
        
        if char in ["'", '"'] and (i == 0 or values_content[i-1] != '\\'):
            if not in_quotes:
                in_quotes = True
                quote_char = char
            elif char == quote_char:
                in_quotes = False
                quote_char = None
        elif char == '{' and not in_quotes:
            bracket_count += 1
        elif char == '}' and not in_quotes:
            bracket_count -= 1
        elif char == ',' and not in_quotes and bracket_count == 0:
            values.append(current_value.strip())
            current_value = ""
            i += 1
            continue
        
        current_value += char
        i += 1
    
    # 添加最后一个值
    if current_value.strip():
        values.append(current_value.strip())
    
    # 清理值（去除引号）
    cleaned_values = []
    for value in values:
        value = value.strip()
        if value.startswith("'") and value.endswith("'"):
            value = value[1:-1]
        elif value.startswith('"') and value.endswith('"'):
            value = value[1:-1]
        cleaned_values.append(value)
    
    # 根据字段顺序提取product_id和datasheet_url
    # 字段顺序: product_id, model, brand_id, price_key, stock_key, datasheet_url, ...
    if len(cleaned_values) >= 6:
        product_id = cleaned_values[0]
        datasheet_url = cleaned_values[5]
        return product_id, datasheet_url
    
    return None, None


def generate_update_statements(product_data: List[Tuple[str, str]]) -> str:
    """
    生成UPDATE语句
    """
    if not product_data:
        return ""
    
    update_statements = []
    update_statements.append("-- Cloudflare D1 数据库 datasheet_url 更新语句")
    update_statements.append(f"-- 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    update_statements.append("")
    
    for product_id, datasheet_url in product_data:
        if product_id and datasheet_url:
            # 转义单引号
            escaped_url = datasheet_url.replace("'", "''")
            escaped_id = product_id.replace("'", "''")
            
            update_stmt = f"UPDATE products202503 SET datasheet_url = '{escaped_url}' WHERE product_id = '{escaped_id}';"
            update_statements.append(update_stmt)
    
    return "\n".join(update_statements)


def process_sql_files(input_folder: str, output_file: str = None) -> None:
    """
    处理指定文件夹中的所有SQL文件
    """
    # 确保输入文件夹存在
    if not os.path.exists(input_folder):
        print(f"错误: 文件夹 '{input_folder}' 不存在")
        return
    
    # 查找所有SQL文件
    sql_pattern = os.path.join(input_folder, "*.sql")
    sql_files = glob.glob(sql_pattern)
    
    if not sql_files:
        print(f"在文件夹 '{input_folder}' 中未找到SQL文件")
        return
    
    print(f"找到 {len(sql_files)} 个SQL文件")
    
    all_product_data = []
    
    # 处理每个SQL文件
    for sql_file in sql_files:
        print(f"处理文件: {os.path.basename(sql_file)}")
        
        try:
            with open(sql_file, 'r', encoding='utf-8') as f:
                sql_content = f.read()
        except UnicodeDecodeError:
            # 尝试其他编码
            try:
                with open(sql_file, 'r', encoding='gbk') as f:
                    sql_content = f.read()
            except UnicodeDecodeError:
                print(f"警告: 无法读取文件 {sql_file}，跳过")
                continue
        
        # 提取INSERT语句
        insert_statements = extract_insert_statements(sql_content)
        print(f"  找到 {len(insert_statements)} 条INSERT语句")
        
        # 解析每个INSERT语句
        for insert_stmt in insert_statements:
            product_id, datasheet_url = parse_insert_statement(insert_stmt)
            if product_id and datasheet_url:
                all_product_data.append((product_id, datasheet_url))
    
    print(f"\n总共提取到 {len(all_product_data)} 条产品数据")
    
    if not all_product_data:
        print("没有找到有效的产品数据")
        return
    
    # 生成UPDATE语句
    update_sql = generate_update_statements(all_product_data)
    
    # 确定输出文件名
    if output_file is None:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f"update_datasheet_urls_{timestamp}.sql"
    
    # 写入输出文件
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(update_sql)
        print(f"\nUPDATE语句已生成到文件: {output_file}")
        print(f"包含 {len(all_product_data)} 条UPDATE语句")
    except Exception as e:
        print(f"错误: 无法写入输出文件 {output_file}: {e}")


def main():
    """
    主函数
    """
    # 固定输入文件夹路径
    input_folder = r"M:\完整sql\NEWproducts202503"

    # 固定输出文件名
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_file = f"update_datasheet_urls_{timestamp}.sql"

    print("SQL Datasheet URL Updater")
    print("=" * 50)
    print(f"输入文件夹: {input_folder}")
    print(f"输出文件: {output_file}")
    print()

    # 处理SQL文件
    process_sql_files(input_folder, output_file)

    print("\n处理完成!")


if __name__ == "__main__":
    main()
