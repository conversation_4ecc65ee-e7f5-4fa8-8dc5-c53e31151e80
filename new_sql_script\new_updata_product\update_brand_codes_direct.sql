-- 批量更新产品品牌编码
-- 生成时间: 2025-07-15 03:36:01
-- 总计更新语句: 126 条

BEGIN TRANSACTION;

-- 更新语句 1
UPDATE products202503 
SET brand_id = 'CEB000014', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '12358';

-- 更新语句 2
UPDATE products202503 
SET brand_id = 'CEB000182', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '15906';

-- 更新语句 3
UPDATE products202503 
SET brand_id = 'CEB000110', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '12344';

-- 更新语句 4
UPDATE products202503 
SET brand_id = 'CEB000194', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '1041';

-- 更新语句 5
UPDATE products202503 
SET brand_id = 'CEB000122', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '15003';

-- 更新语句 6
UPDATE products202503 
SET brand_id = 'CEB000086', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '13081';

-- 更新语句 7
UPDATE products202503 
SET brand_id = 'CEB000648', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '13566';

-- 更新语句 8
UPDATE products202503 
SET brand_id = 'CEB000135', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '16104';

-- 更新语句 9
UPDATE products202503 
SET brand_id = 'CEB001664', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '1227';

-- 更新语句 10
UPDATE products202503 
SET brand_id = 'CEB000206', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '13570';

-- 更新语句 11
UPDATE products202503 
SET brand_id = 'CEB000211', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '13577';

-- 更新语句 12
UPDATE products202503 
SET brand_id = 'CEB000046', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '15577';

-- 更新语句 13
UPDATE products202503 
SET brand_id = 'CEB000052', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '1266';

-- 更新语句 14
UPDATE products202503 
SET brand_id = 'CEB002910', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '205';

-- 更新语句 15
UPDATE products202503 
SET brand_id = 'CEB000226', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '15168';

-- 更新语句 16
UPDATE products202503 
SET brand_id = 'CEB000233', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '13583';

-- 更新语句 17
UPDATE products202503 
SET brand_id = 'CEB000235', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '13585';

-- 更新语句 18
UPDATE products202503 
SET brand_id = 'CEB000240', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '16244';

-- 更新语句 19
UPDATE products202503 
SET brand_id = 'CEB000241', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '16245';

-- 更新语句 20
UPDATE products202503 
SET brand_id = 'CEB000249', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '16277';

-- 更新语句 21
UPDATE products202503 
SET brand_id = 'CEB000262', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '17054';

-- 更新语句 22
UPDATE products202503 
SET brand_id = 'CEB000317', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '1273';

-- 更新语句 23
UPDATE products202503 
SET brand_id = 'CEB003111', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '234';

-- 更新语句 24
UPDATE products202503 
SET brand_id = 'CEB000380', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '11543';

-- 更新语句 25
UPDATE products202503 
SET brand_id = 'CEB002312', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '12779';

-- 更新语句 26
UPDATE products202503 
SET brand_id = 'CEB000399', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '15554';

-- 更新语句 27
UPDATE products202503 
SET brand_id = 'CEB000400', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '14109';

-- 更新语句 28
UPDATE products202503 
SET brand_id = 'CEB000421', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '135';

-- 更新语句 29
UPDATE products202503 
SET brand_id = 'CEB001895', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '233';

-- 更新语句 30
UPDATE products202503 
SET brand_id = 'CEB000460', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '13001';

-- 更新语句 31
UPDATE products202503 
SET brand_id = 'CEB002502', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '13902';

-- 更新语句 32
UPDATE products202503 
SET brand_id = 'CEB000476', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '11454';

-- 更新语句 33
UPDATE products202503 
SET brand_id = 'CEB000482', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '13112';

-- 更新语句 34
UPDATE products202503 
SET brand_id = 'CEB000522', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '14446';

-- 更新语句 35
UPDATE products202503 
SET brand_id = 'CEB000679', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '11906';

-- 更新语句 36
UPDATE products202503 
SET brand_id = 'CEB001025', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '11930';

-- 更新语句 37
UPDATE products202503 
SET brand_id = 'CEB000552', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '15379';

-- 更新语句 38
UPDATE products202503 
SET brand_id = 'CEB000575', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '15489';

-- 更新语句 39
UPDATE products202503 
SET brand_id = 'CEB001707', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '12772';

-- 更新语句 40
UPDATE products202503 
SET brand_id = 'CEB000637', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '13808';

-- 更新语句 41
UPDATE products202503 
SET brand_id = 'CEB000702', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '12922';

-- 更新语句 42
UPDATE products202503 
SET brand_id = 'CEB003268', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '15364';

-- 更新语句 43
UPDATE products202503 
SET brand_id = 'CEB000813', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '13505';

-- 更新语句 44
UPDATE products202503 
SET brand_id = 'CEB001874', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '14220';

-- 更新语句 45
UPDATE products202503 
SET brand_id = 'CEB000847', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '12227';

-- 更新语句 46
UPDATE products202503 
SET brand_id = 'CEB000865', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '16879';

-- 更新语句 47
UPDATE products202503 
SET brand_id = 'CEB000798', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '13599';

-- 更新语句 48
UPDATE products202503 
SET brand_id = 'CEB000900', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '17027';

-- 更新语句 49
UPDATE products202503 
SET brand_id = 'CEB000924', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '17057';

-- 更新语句 50
UPDATE products202503 
SET brand_id = 'CEB000999', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '16935';

-- 更新语句 51
UPDATE products202503 
SET brand_id = 'CEB001020', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '16589';

-- 更新语句 52
UPDATE products202503 
SET brand_id = 'CEB001040', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '420';

-- 更新语句 53
UPDATE products202503 
SET brand_id = 'CEB001160', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '13014';

-- 更新语句 54
UPDATE products202503 
SET brand_id = 'CEB001224', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '13121';

-- 更新语句 55
UPDATE products202503 
SET brand_id = 'CEB001271', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '15600';

-- 更新语句 56
UPDATE products202503 
SET brand_id = 'CEB001281', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '16233';

-- 更新语句 57
UPDATE products202503 
SET brand_id = 'CEB001310', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '12495';

-- 更新语句 58
UPDATE products202503 
SET brand_id = 'CEB002248', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '13827';

-- 更新语句 59
UPDATE products202503 
SET brand_id = 'CEB001353', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '13534';

-- 更新语句 60
UPDATE products202503 
SET brand_id = 'CEB001358', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '12862';

-- 更新语句 61
UPDATE products202503 
SET brand_id = 'CEB001369', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '11327';

-- 更新语句 62
UPDATE products202503 
SET brand_id = 'CEB001376', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '15248';

-- 更新语句 63
UPDATE products202503 
SET brand_id = 'CEB001385', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '16886';

-- 更新语句 64
UPDATE products202503 
SET brand_id = 'CEB001398', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '15736';

-- 更新语句 65
UPDATE products202503 
SET brand_id = 'CEB001400', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '13531';

-- 更新语句 66
UPDATE products202503 
SET brand_id = 'CEB001428', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '16257';

-- 更新语句 67
UPDATE products202503 
SET brand_id = 'CEB001468', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '13211';

-- 更新语句 68
UPDATE products202503 
SET brand_id = 'CEB001488', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '17053';

-- 更新语句 69
UPDATE products202503 
SET brand_id = 'CEB001503', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '15254';

-- 更新语句 70
UPDATE products202503 
SET brand_id = 'CEB001876', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '13591';

-- 更新语句 71
UPDATE products202503 
SET brand_id = 'CEB001648', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '17079';

-- 更新语句 72
UPDATE products202503 
SET brand_id = 'CEB001652', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '17076';

-- 更新语句 73
UPDATE products202503 
SET brand_id = 'CEB001737', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '14031';

-- 更新语句 74
UPDATE products202503 
SET brand_id = 'CEB002610', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '16052';

-- 更新语句 75
UPDATE products202503 
SET brand_id = 'CEB001889', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '13464';

-- 更新语句 76
UPDATE products202503 
SET brand_id = 'CEB001922', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '11475';

-- 更新语句 77
UPDATE products202503 
SET brand_id = 'CEB001950', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '13177';

-- 更新语句 78
UPDATE products202503 
SET brand_id = 'CEB001988', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '12385';

-- 更新语句 79
UPDATE products202503 
SET brand_id = 'CEB002001', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '13670';

-- 更新语句 80
UPDATE products202503 
SET brand_id = 'CEB002025', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '16972';

-- 更新语句 81
UPDATE products202503 
SET brand_id = 'CEB002096', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '15547';

-- 更新语句 82
UPDATE products202503 
SET brand_id = 'CEB002126', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '16905';

-- 更新语句 83
UPDATE products202503 
SET brand_id = 'CEB002129', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '15214';

-- 更新语句 84
UPDATE products202503 
SET brand_id = 'CEB002141', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '15299';

-- 更新语句 85
UPDATE products202503 
SET brand_id = 'CEB002155', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '16234';

-- 更新语句 86
UPDATE products202503 
SET brand_id = 'CEB002282', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '11998';

-- 更新语句 87
UPDATE products202503 
SET brand_id = 'CEB002296', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '16588';

-- 更新语句 88
UPDATE products202503 
SET brand_id = 'CEB002342', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '16198';

-- 更新语句 89
UPDATE products202503 
SET brand_id = 'CEB002348', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '16200';

-- 更新语句 90
UPDATE products202503 
SET brand_id = 'CEB002368', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '15351';

-- 更新语句 91
UPDATE products202503 
SET brand_id = 'CEB002411', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '16264';

-- 更新语句 92
UPDATE products202503 
SET brand_id = 'CEB002481', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '16932';

-- 更新语句 93
UPDATE products202503 
SET brand_id = 'CEB002517', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '15597';

-- 更新语句 94
UPDATE products202503 
SET brand_id = 'CEB002580', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '16601';

-- 更新语句 95
UPDATE products202503 
SET brand_id = 'CEB002585', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '16867';

-- 更新语句 96
UPDATE products202503 
SET brand_id = 'CEB002596', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '12983';

-- 更新语句 97
UPDATE products202503 
SET brand_id = 'CEB002621', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '15994';

-- 更新语句 98
UPDATE products202503 
SET brand_id = 'CEB002622', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '15378';

-- 更新语句 99
UPDATE products202503 
SET brand_id = 'CEB002701', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '13024';

-- 更新语句 100
UPDATE products202503 
SET brand_id = 'CEB002770', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '15911';

-- 更新语句 101
UPDATE products202503 
SET brand_id = 'CEB002871', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '11596';

-- 更新语句 102
UPDATE products202503 
SET brand_id = 'CEB002897', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '753';

-- 更新语句 103
UPDATE products202503 
SET brand_id = 'CEB002900', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '15939';

-- 更新语句 104
UPDATE products202503 
SET brand_id = 'CEB002919', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '16938';

-- 更新语句 105
UPDATE products202503 
SET brand_id = 'CEB002922', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '16269';

-- 更新语句 106
UPDATE products202503 
SET brand_id = 'CEB002945', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '15766';

-- 更新语句 107
UPDATE products202503 
SET brand_id = 'CEB002968', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '16619';

-- 更新语句 108
UPDATE products202503 
SET brand_id = 'CEB002988', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '17019';

-- 更新语句 109
UPDATE products202503 
SET brand_id = 'CEB003029', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '975';

-- 更新语句 110
UPDATE products202503 
SET brand_id = 'CEB003066', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '16632';

-- 更新语句 111
UPDATE products202503 
SET brand_id = 'CEB003074', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '14058';

-- 更新语句 112
UPDATE products202503 
SET brand_id = 'CEB003084', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '16040';

-- 更新语句 113
UPDATE products202503 
SET brand_id = 'CEB003169', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '16047';

-- 更新语句 114
UPDATE products202503 
SET brand_id = 'CEB003173', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '16959';

-- 更新语句 115
UPDATE products202503 
SET brand_id = 'CEB003197', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '17078';

-- 更新语句 116
UPDATE products202503 
SET brand_id = 'CEB003210', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '15578';

-- 更新语句 117
UPDATE products202503 
SET brand_id = 'CEB003226', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '11699';

-- 更新语句 118
UPDATE products202503 
SET brand_id = 'CEB003235', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '12027';

-- 更新语句 119
UPDATE products202503 
SET brand_id = 'CEB003274', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '15298';

-- 更新语句 120
UPDATE products202503 
SET brand_id = 'CEB003279', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '16986';

-- 更新语句 121
UPDATE products202503 
SET brand_id = 'CEB003284', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '16280';

-- 更新语句 122
UPDATE products202503 
SET brand_id = 'CEB003287', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '15377';

-- 更新语句 123
UPDATE products202503 
SET brand_id = 'CEB003288', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '16231';

-- 更新语句 124
UPDATE products202503 
SET brand_id = 'CEB003247', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '13295';

-- 更新语句 125
UPDATE products202503 
SET brand_id = 'CEB003293', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '16598';

-- 更新语句 126
UPDATE products202503 
SET brand_id = 'CEB003309', updated_at = '2025-07-15 03:36:01' 
WHERE brand_id = '17073';

COMMIT;
